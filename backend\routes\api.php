<?php

use App\Http\Controllers\AdminController;
use App\Http\Controllers\AnalysisController;
use App\Http\Controllers\AnalystController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\FactureController;
use App\Http\Controllers\ClientController;
use App\Http\Controllers\SampleController;
use App\Http\Controllers\PasswordResetController;
use App\Http\Controllers\DirectorController;
use App\Http\Controllers\ReceptionistController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\DemandeController;
use App\Http\Controllers\FicheController;
use App\Http\Controllers\RegistreSuiviController;
use App\Http\Controllers\DerogationController;
use App\Http\Controllers\RapportController;
use App\Http\Controllers\ResultController;
use App\Http\Controllers\PaymentController;
use App\Models\Demande;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Str;
use Illuminate\Support\Facades\Broadcast;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Route;

// ✅ Register Broadcast Routes
Broadcast::routes(['middleware' => ['auth:sanctum']]);

// ✅ Get Authenticated User Information
Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// ✅ Authentication Routes
Route::post('register', [AuthController::class, 'register']);
Route::post('login', [AuthController::class, 'login']);
Route::post('logout', [AuthController::class, 'logout'])->middleware('auth:sanctum');

// ✅ Admin Routes (Protected by Sanctum & Admin Middleware)
Route::middleware(['auth:sanctum', \App\Http\Middleware\AdminMiddleware::class])->group(function () {
    Route::apiResource('admin', AdminController::class);
});

// ✅ User Management Routes (Accessible by Admin and Receptionist)
Route::middleware(['auth:sanctum'])->group(function () {
    Route::get('/users', [AdminController::class, 'index']);
    Route::post('/users', [AdminController::class, 'store']);
    Route::get('/users/{id}', [AdminController::class, 'show']);
    Route::put('/users/{id}', [AdminController::class, 'update']);
    Route::delete('/users/{id}', [AdminController::class, 'destroy']);
});

// ✅ Password Reset Routes
Route::get('/forgot-password', [PasswordResetController::class, 'showLinkRequestForm']);
Route::post('/forgot-password', [PasswordResetController::class, 'sendResetLinkEmail']);
Route::get('/reset-password/{token}', [PasswordResetController::class, 'showResetForm']);
Route::post('/reset-password', [PasswordResetController::class, 'reset']);

// ✅ Demand Routes
Route::post('/demandes', [DemandeController::class, 'store']);
Route::get('/demandesUser', [DemandeController::class, 'getUserDemandes']);

// ✅ Authentication Middleware for Protected Routes
Route::middleware(['auth:sanctum'])->group(function () {

    // ✅ Client Actions
    Route::post('/demandes', [DemandeController::class, 'store']);

    // ✅ Receptionist Actions




});

// Get all notifications for the logged-in user
Route::patch('/receptionist/demandes/{id}/ongoing', [ReceptionistController::class, 'updateStatusToOngoing']);
Route::patch('/receptionist/demandes/{id}/validate', [ReceptionistController::class, 'validateDemandeFinale']);
Route::patch('/receptionist/demandes/{id}/reject', [ReceptionistController::class, 'updateStatusToRejected']);
Route::patch('/receptionist/demandes/{id}/validated-with-derogation', [ReceptionistController::class, 'updateStatusToValidatedWithDerogation']);
Route::get('/receptionist/notifications/new', [ReceptionistController::class, 'getNewDemandeNotifications']);
Route::get('/receptionist/notifications/validatedDirector', [NotificationController::class, 'getValidatedReceptionist']);
Route::get('/receptionist/notifications/rejectedFromDirector', [NotificationController::class, 'getRejectedReceptionist']);
Route::get('/receptionist/notifications/results', [NotificationController::class, 'getResultsNotification']);
Route::get('/client/notifications/samples', [NotificationController::class, 'getBringSampleNotifications']);
Route::get('/client/notifications/validated', [NotificationController::class, 'getValidatedNotifications']);
Route::get('/client/notifications/validatedDirector', [NotificationController::class, 'getValidatedClient']);

Route::get('/client/notifications/validatedFromDirector', [NotificationController::class, 'getValidatedFromDirectorNotifications']);
Route::get('/client/notifications/rejectedFromDirector', [NotificationController::class, 'getRejectedClient']);
Route::get('/client/notifications/paymentrejected', [NotificationController::class, 'getDevisRejectionNotification']);
Route::get('/client/notifications/paymentapproved', [NotificationController::class, 'getDevisValidationNotification']);
Route::get('/client/notifications/rapport', [NotificationController::class, 'getRapportClientNotification']);

//devis details for demande
Route::get('/devis/{demandeId}', [DemandeController::class, 'getAllDevisForDemande']);

// Route to get details of a specific Devis by its ID
Route::get('/devis/details/{demandeID}', [ReceptionistController::class, 'getDevisDetails']);
Route::post('/devis/send/{demandeID}', [ReceptionistController::class, 'sendDevis']);
Route::put('/devis/{demandeId}/analyse/{analyse}', [DemandeController::class, 'updateAnalysePrice']);
// Route to get all Devis for a specific user by user ID
Route::get('/user/devis', [ReceptionistController::class, 'getUserDevis']);

// Route to get all Devis for all users
Route::get('/devis/all', [DemandeController::class, 'getAllDevis']);
Route::get('/director/notifications/derogation', [DirectorController::class, 'getDerogationNotifications']);
Route::get('/director/get-derogation-infos/{demande_id}', [DirectorController::class, 'getDerogationsByDemandeId']);
Route::match(['get', 'post'], '/receptionist/create-derogation/{demande_id}', [ReceptionistController::class, 'createDerogation']);


Route::patch('/director/derogations/{demande_id}/validate', [DirectorController::class, 'validateDemandeFinale']);
Route::patch('/director/derogations/{demande_id}/reject', [DirectorController::class, 'rejectDerogation']);
// Mark a notification as read
Route::patch('/notifications/{id}/read', [NotificationController::class, 'markAsRead']);

Route::get('/demande/{demande_id}', [DemandeController::class, 'getDemandeDetails']);
Route::get('/demandes/{identifier}', [DemandeController::class, 'getDemandeBy']);
Route::get('/demandes', [DemandeController::class, 'getAllDemandes']);
Route::get('/demandess/valid', [DemandeController::class, 'validDemandes']);
Route::get('/demandesAll', [DemandeController::class, 'getAllDemande']);
Route::get('/director/notifications/derogated', [DemandeController::class, 'getDerogatedNotifications']);
Route::get('/userdetails/{id}',[DemandeController::class,'getUserById']);
Route::get('/derogations/{demande_id}', [DemandeController::class, 'getDerogationDetails']);
Route::get('/derogations', [DemandeController::class, 'getAllDerogations']);
Route::get('/notifications/demande/{demandeId}', [NotificationController::class, 'getNotificationByDemandeId']);
Route::get('/notifications/demande/rejected/{demandeId}', [NotificationController::class, 'getRejected']);
Route::get('/notifications/demande/bringsample{demandeId}', [NotificationController::class, 'getBringSample']);
Route::get('/receptionnist/notification/devis', [NotificationController::class, 'getDevisNotification']);
Route::get('/receptionnist/notification/payment/approved', [NotificationController::class, 'getPaymentApprovedNotification']);
Route::get('/receptionnist/notification/payment/rejected', [NotificationController::class, 'getPaymentRejectedNotification']);

Route::get('/fiches', [FicheController::class, 'index']); // List all fiches de transmission
Route::get('/fiches/analysts', [FicheController::class, 'getAnalystFiches']); // List all fiches de transmission sented to analyst
Route::post('/fiches/create/{demande_id}', [FicheController::class, 'createFiches']); // Generate fiches for a demande
Route::get('/fiches/{fiche_transmission_id}', [FicheController::class, 'show']); // View all fiches related to a fiche de transmission


Route::post('/registre-suivis/{demande_id}', [RegistreSuiviController::class, 'store']);
Route::get('/registre-suivis', [RegistreSuiviController::class, 'index']); // Get summarized view
Route::get('/registre-suivis/{id}', [RegistreSuiviController::class, 'show']); // Get details
Route::put('/registres/{id}', [RegistreSuiviController::class, 'update']); // Update registre fields

Route::post('/users/details', [AuthController::class, 'getUsersDetails']);
Route::post('/fiches/{id}/send', [FicheController::class, 'sendFiche']);

Route::post('/create-rapport/{demandeId}', [RapportController::class,'createRapport']);
Route::get('/rapports/{rapportId}', [RapportController::class,'getRapportDetails']);
Route::get('/rapports', [RapportController::class,'getAllRapports']);
Route::get('/receptionist/rapports', [RapportController::class,'getRapportsReceptionist']);
Route::get('/receptionist/notif/rapports', [RapportController::class,'getNotifRapportsReceptionist']);
Route::get('/director/rapports', [RapportController::class,'getRapportsDirector']);
Route::get('/rapports/sent', [RapportController::class,'getSentRapports']);
Route::put('/update-rapport-analysis/{id}', [RapportController::class, 'updateAnalysis']);
Route::get('/rapport/id/demande/{demandeId}', [RapportController::class, 'getRapportIdByDemandeId']);
Route::put('/client/rapport/send/{id}', [RapportController::class, 'sendRapportClient']); // ✅ Send a report (update status)
Route::put('/rapport/director/{id}', [RapportController::class, 'sendRapportToDirector']); // ✅ Send a report (update status)
Route::get('/responsable/notifications', [NotificationController::class, 'getFicheTransmissionNotifications']); // ✅ Send a report (update status)

// Results Routes

// General authenticated routes for results

    Route::get('/results', [ResultController::class, 'index']); // Get all results
    Route::post('/results', [ResultController::class, 'store']); // Store a new result
    Route::get('/results/demande/{demandeId}', [ResultController::class, 'getResultsByDemandeId']); // Get results for a specific demande
    Route::get('/results/analyse/{demandeId}', [ResultController::class, 'getResultsAnalyse']); // Get results for a specific demande
    Route::get('/results/download/{resultId}', [ResultController::class, 'downloadResults']); // Download results file
    Route::post('/demande/{demandeId}/results', [ResultController::class, 'submitResults']); // Submit results for a specific demande
    Route::delete('/results/{resultId}', [ResultController::class, 'destroy']); // Delete a result

// Analyst-specific routes
Route::middleware(['auth:sanctum'])->group(function () {
    Route::post('/analyst/results/submit/{demandeId}', [ResultController::class, 'submitResults']); // Submit results for a specific demande
    Route::get('/analyst/results', [ResultController::class, 'index']); // Get all results (analyst can view all)
    Route::delete('/analyst/results/{resultId}', [ResultController::class, 'destroy']); // Delete a result (analyst only)
});

// Client can view their own results
Route::middleware(['auth:sanctum', \App\Http\Middleware\ClientMiddleware::class])->group(function () {
    Route::get('/client/results', [ResultController::class, 'getClientResults']); // Get results for the authenticated client
});

// Analyses Management Routes

    Route::get('/analyses', [AnalysisController::class, 'index']);
    Route::post('/analyses', [AnalysisController::class, 'store']);
    Route::get('/analyses/{id}', [AnalysisController::class, 'show']);
    Route::put('/analyses/{id}', [AnalysisController::class, 'update']);
    Route::delete('/analyses/{id}', [AnalysisController::class, 'destroy']);

Route::get('/director/notifications/rapports', [NotificationController::class, 'getRapportNotificationsDirector']); // ✅ Send a report (update status)
Route::get('/receptionist/notifications/rapports', [NotificationController::class, 'getRapportNotificationsReceptionist']); // ✅ Send a report (update status)
Route::get('/receptionist/notifications/rapports/checked', [NotificationController::class, 'getCheckedRapportNotificationsReceptionist']); // ✅ Send a report (update status)
Route::put('/rapports/reject/{id}', [RapportController::class, 'rejectRapport']);
Route::put('/rapports/validate/{id}', [RapportController::class, 'validateRapport']);


Route::post('/facture/create/{demande_id}', [DemandeController::class, 'create']);
Route::get('/factures', [FactureController::class, 'index']);
Route::get('/factures/{id}', [FactureController::class, 'show']);
Route::get('/facture/valid-reports', [FactureController::class, 'getValidReports']);

// Results routes
Route::get('/results', [FactureController::class, 'getAllResultatClients']);
Route::get('/results/{demandeId}', [FactureController::class, 'getResultatClient']);
Route::post('/results/{demandeId}/upload', [FactureController::class, 'uploadFile']);
Route::put('/results/{demandeId}/send', [FactureController::class, 'sendResultsToClient']);
Route::delete('/results/{demandeId}/report', [FactureController::class, 'deleteReportFile']);
Route::delete('/results/{demandeId}/invoice', [FactureController::class, 'deleteInvoiceFile']);

// Payment routes - Not protected by middleware (authentication handled in controller)
// List and create payments
Route::get('/payments', [PaymentController::class, 'getAllPayments']); // Get all payments with details
Route::post('/payments', [PaymentController::class, 'store']); // Upload a new payment proof

// Get payments by demande
Route::get('/payments/demande/{demandeId}', [PaymentController::class, 'getPaymentsByDemandeId']); // Get payments for a specific demande

// Operations on specific payments by ID
Route::get('/payments/{paymentId}/download', [PaymentController::class, 'downloadPaymentProofById']); // Download a payment proof file by ID
Route::get('/payments/{paymentId}', [PaymentController::class, 'show']); // Get a specific payment
Route::delete('/payments/{paymentId}', [PaymentController::class, 'destroy']); // Delete a payment
Route::put('/payments/{paymentId}/status', [PaymentController::class, 'updateStatus']); // Update payment status (approve/reject)
Route::get('/payment-proof/{paymentId}', [PaymentController::class, 'servePaymentProof']);


// Public payment routes (for file access)
// File operations with direct paths
Route::get('/payments/view/{filePath}', [PaymentController::class, 'viewPaymentProof']); // View a payment proof file
Route::get('/payments/download/{filePath}', [PaymentController::class, 'downloadPaymentProof']); // Download a payment proof file

// Client-specific payment routes - No middleware
Route::get('/client/payments', [PaymentController::class, 'getUserPayments']); // Get payments for the authenticated client

Route::get('/test-email', function () {
    Mail::raw('Hello', function ($message) {
        $message->to('<EMAIL>')->subject('Test Email');
    });
    return 'Email sent';
});

// Diagnostic route to check storage access
Route::get('/check-storage', function () {
    $publicPath = public_path('storage');
    $storagePath = storage_path('app/public');

    $result = [
        'public_path_exists' => file_exists($publicPath),
        'storage_path_exists' => file_exists($storagePath),
        'public_path' => $publicPath,
        'storage_path' => $storagePath,
        'is_symlink' => is_link($publicPath),
        'symlink_target' => is_link($publicPath) ? readlink($publicPath) : null,
    ];

    return response()->json($result);
});

// Diagnostic route to check if a specific file exists
Route::get('/check-file', function (Request $request) {
    $filePath = $request->query('path');
    if (!$filePath) {
        return response()->json(['error' => 'No file path provided'], 400);
    }

    // Check in storage/app/public
    $storagePath = storage_path('app/public/' . $filePath);
    $publicPath = public_path('storage/' . $filePath);

    $result = [
        'file_path' => $filePath,
        'storage_path' => $storagePath,
        'public_path' => $publicPath,
        'storage_exists' => file_exists($storagePath),
        'public_exists' => file_exists($publicPath),
        'storage_readable' => is_readable($storagePath),
        'public_readable' => is_readable($publicPath),
    ];

    return response()->json($result);
});

// Direct route to serve files without authentication (for testing)
Route::get('/direct-file/{filePath}', function ($filePath) {
    // Decode the file path
    $filePath = urldecode($filePath);

    // Check if the file exists in storage
    $fullFilePath = storage_path('app/public/' . $filePath);

    // If file not found, try with payments/ prefix
    if (!file_exists($fullFilePath) && !str_starts_with($filePath, 'payments/')) {
        $alternativePath = 'payments/' . $filePath;
        $alternativeFullPath = storage_path('app/public/' . $alternativePath);

        if (file_exists($alternativeFullPath)) {
            $fullFilePath = $alternativeFullPath;
            $filePath = $alternativePath;
        }
    }

    // If still not found, try without payments/ prefix
    if (!file_exists($fullFilePath) && str_starts_with($filePath, 'payments/')) {
        $alternativePath = substr($filePath, 9); // Remove 'payments/'
        $alternativeFullPath = storage_path('app/public/' . $alternativePath);

        if (file_exists($alternativeFullPath)) {
            $fullFilePath = $alternativeFullPath;
            $filePath = $alternativePath;
        }
    }

    // If still not found, return 404
    if (!file_exists($fullFilePath)) {
        return response()->json([
            'status' => 'error',
            'message' => 'File not found',
            'path_tried' => $filePath,
            'full_path_tried' => $fullFilePath
        ], 404);
    }

    // Get the file's mime type
    $mimeType = mime_content_type($fullFilePath);

    // Add CORS headers for image files
    $headers = ['Content-Type' => $mimeType];
    if (strpos($mimeType, 'image/') === 0) {
        $headers['Access-Control-Allow-Origin'] = '*';
        $headers['Access-Control-Allow-Methods'] = 'GET, OPTIONS';
        $headers['Access-Control-Allow-Headers'] = 'Origin, Content-Type, Accept';
    }

    // Serve the file directly with the appropriate mime type
    return response()->file($fullFilePath, $headers);
})->where('filePath', '.*'); // Allow slashes in the path

// Additional route for image files specifically
Route::get('/image-file/{filePath}', function ($filePath) {
    // Decode the file path
    $filePath = urldecode($filePath);

    // Try multiple locations
    $possiblePaths = [
        $filePath,
        'payments/' . $filePath,
        substr($filePath, strpos($filePath, 'payments/') !== false ? 9 : 0)
    ];

    foreach ($possiblePaths as $path) {
        $fullFilePath = storage_path('app/public/' . $path);
        if (file_exists($fullFilePath)) {
            // Get the file's mime type
            $mimeType = mime_content_type($fullFilePath);

            // Only serve image files
            if (strpos($mimeType, 'image/') === 0) {
                return response()->file($fullFilePath, [
                    'Content-Type' => $mimeType,
                    'Access-Control-Allow-Origin' => '*',
                    'Access-Control-Allow-Methods' => 'GET, OPTIONS',
                    'Access-Control-Allow-Headers' => 'Origin, Content-Type, Accept'
                ]);
            }
        }
    }

    return response()->json([
        'status' => 'error',
        'message' => 'Image file not found',
        'paths_tried' => $possiblePaths
    ], 404);
})->where('filePath', '.*'); // Allow slashes in the path