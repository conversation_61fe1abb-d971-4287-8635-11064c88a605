<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Rapport extends Model
{
    protected $fillable = ['demande_id', 'creation_date', 'status', 'demande_numero','status_director','validation'];

    public function analyses()
    {
        return $this->hasMany(RapportAnalyse::class);
    }
    public function demande()
    {
        return $this->belongsTo(Demande::class, 'demande_id', 'id');
    }
}

