<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Demande;
use App\Models\FicheTransmission;
use App\Models\Rapport;
use App\Models\RapportAnalyse;
use Illuminate\Support\Facades\DB;
use App\Models\Notification;
use App\Models\Sample;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use App\Mail\ValidateRapportEmail;
use App\Mail\RapportDirectorEmail;
use App\Mail\RapportClientEmail;
use App\Models\User;

class RapportController extends Controller
{

    public function validateRapport($id)
    {
        // ✅ Find the Rapport
        $rapport = Rapport::find($id);
        $demande = Demande::find($rapport->demande_id);
        if (!$rapport) {
            return response()->json(['message' => 'Rapport not found'], 404);
        }
        $notification=Notification::create([

            'title' => 'Rapport Approuvé',
            'message' =>'Validation du Rapport pour la demande '.$rapport->demande_numero.' ',
            'demande_id'=>$rapport->demande_id,
            'type' => 'rapport validation',
            'rapport_id'=>$rapport->id
        ]);
        $userID=$demande->user_id;
        $user=User::find($userID);
    $receptionists=User::where('role', 'receptionist')->first();
    // Send the email
    Mail::to($receptionists->email)->send(new ValidateRapportEmail($notification, $demande, $user));
        // ✅ Update the status in the database
        $rapport->update(['validation' => 1]);
        $demande->update(['validation' => 1]);
        return response()->json(['message' => 'Rapport envoyé avec succès', 'status' => 'sented']);
    }
    public function rejectRapport($id)
    {
        // Find the Rapport
        $rapport = Rapport::find($id);
        if (!$rapport) {
            return response()->json(['message' => 'Rapport not found'], 404);
        }

        // Find the associated Demande using the foreign key demande_id from Rapport
        $demande = Demande::find($rapport->demande_id);
        if (!$demande) {
            return response()->json(['message' => 'Demande not found'], 404);
        }

        // Create a notification
        Notification::create([
            'title' => 'Rapport Rejeté',
            'message' => 'Rejet du Rapport de la demande ' . $rapport->demande_numero . ' ',
            'demande_id' => $rapport->demande_id,
            'type' => 'rapport validation',
            'rapport_id' => $rapport->id
        ]);

        // Update the validation status in the database
        $rapport->update(['validation' => 0]);
        $demande->update(['validation' => 0]);

        // Return a success response
        return response()->json(['message' => 'Rapport rejeté avec succès', 'status' => 'rejected']);
    }
    public function getRapportIdByDemandeId($demande_numero)
{
    // ✅ Fetch only the `rapport_id` for the given `demande_id`
    $rapport = Rapport::where('demande_numero', $demande_numero)->first();

    // ✅ Check if the report exists
    if (!$rapport) {
        return response()->json(['message' => 'Rapport not found'], 404);
    }

    return response()->json(['rapport_id' => $rapport->id]);
}
    public function updateAnalysis(Request $request, $id)
    {
        $rapportAnalyse = RapportAnalyse::find($id);

        if (!$rapportAnalyse) {
            return response()->json(['error' => 'Rapport Analyse not found'], 404);
        }

        // ✅ Validate only fields that are being updated
        $validatedData = $request->validate([
            'mesurande' => 'nullable|string',
            'unite' => 'nullable|string',
            'limite_acceptabilite' => 'nullable|string',
            'date_analyse' => 'nullable|date'
        ]);

        // ✅ Only update fields that are present in the request
        foreach ($validatedData as $key => $value) {
            if ($value !== null) {
                $rapportAnalyse->$key = $value;
            }
        }

        $rapportAnalyse->save();

        return response()->json([
            'message' => 'Rapport Analyse updated successfully',
            'data' => $rapportAnalyse
        ], 200);
    }
    public function getAllRapports()
{
    // ✅ Retrieve all rapports with demande details
    $rapports = Rapport::select('id', 'demande_id', 'creation_date', 'status','validation','status_director')
        ->with('demande:id,demande_id') // Fetch related demande details
        ->get();

    // ✅ Format response
    $formattedRapports = $rapports->map(function ($rapport) {
        return [
            'id' => $rapport->id,
            'demande_id' => $rapport->demande ? $rapport->demande->demande_id : 'N/A',
            'creation_date' => $rapport->creation_date,
            'status' => $rapport->status,
            'validation' => $rapport->validation,
            'status_director' => $rapport->status_director,
        ];
    });

    return response()->json($formattedRapports);
}
public function sendRapportClient($demande_id)
{
    // Find the demande by demande_id
    $demande = Demande::where('demande_id', $demande_id)->first();

    if (!$demande) {
        return response()->json(['message' => 'Demande not found'], 404);
    }

    // Get the rapport_id and facture_id from the demande
    $rapport_id = $demande->rapport_created;
    $facture_id = $demande->facture_id;

    if (!$rapport_id) {
        return response()->json(['message' => 'Rapport not found for this demande'], 404);
    }

    // Get the rapport to include demande_numero in the notification
    $rapport = Rapport::find($rapport_id);
$rapport->update(['status' => 'sent']);
    if (!$rapport) {
        return response()->json(['message' => 'Rapport not found'], 404);
    }

    // Create a notification
    $notification = Notification::create([
        'title' => 'Rapport Prêt',
        'message' => 'Votre rapport pour la demande ' . $rapport->demande_numero . ' est prêt. Consultez-le dès maintenant !',
        'demande_id' => $demande->demande_numero,
        'type' => 'rapport prêt',
        'rapport_id' => $rapport_id,
        'facture_id' => $facture_id,
        'user_id' => $demande->user_id
    ]);

    // Get the user to send email notification
    $user = User::find($demande->user_id);

    if ($user) {
        // Send email notification (similar to other controllers)
        try {
            Mail::to($user->email)->send(new RapportClientEmail($notification, $rapport, $demande, $user));
        } catch (\Exception $e) {
            // Log the error but continue execution
            Log::error('Failed to send rapport client email: ' . $e->getMessage());
        }
    }

    return response()->json([
        'message' => 'Notification de rapport envoyée avec succès',
        'notification' => $notification
    ]);
}
public function getRapportsReceptionist()
{
    // ✅ Retrieve all rapports with demande details
    $rapports = Rapport::select('id', 'demande_id', 'creation_date', 'status', 'status_director', 'validation','updated_at')
        ->with('demande:id,demande_id,facture_id') // Ensure facture_id is included
        ->where('status', 'sent')
        ->get();

    // ✅ Format response
    $formattedRapports = $rapports->map(function ($rapport) {
        return [
            'id' => $rapport->id,
            'demande_id' => $rapport->demande ? $rapport->demande->demande_id : 'N/A',
            'creation_date' => $rapport->creation_date,
            'status' => $rapport->status,
            'status_director' => $rapport->status_director,
            'validation' => $rapport->validation,
            'reception_date'=>$rapport->updated_at,
            'demande_numero'=>$rapport->demande_numero,
            'facture_id' => $rapport->demande ? $rapport->demande->facture_id : 'N/A' // Ensure facture_id is retrieved correctly
        ];
    });

    return response()->json($formattedRapports);
}

public function getNotifRapportsReceptionist()
    {

        $notifications = Notification::where('type', 'rapport receptionist')
        ->get();
        return response()->json([
            'notifications' => $notifications,
        ]);
    }
public function getRapportsDirector()
{
    // ✅ Retrieve all rapports with demande details
    $rapports = Rapport::select('id', 'demande_id', 'creation_date', 'status','status_director','validation')
        ->with('demande:id,demande_id')
        ->where('status_director', 'sent')// Fetch related demande details
        ->get();


    // ✅ Format response
    $formattedRapports = $rapports->map(function ($rapport) {
        return [
            'id' => $rapport->id,
            'demande_id' => $rapport->demande ? $rapport->demande->demande_id : 'N/A',
            'creation_date' => $rapport->creation_date,
            'status' => $rapport->status,
            'status_director' => $rapport->status_director,
            'validation' => $rapport->validation
        ];
    });

    return response()->json($formattedRapports);
}
public function getRapportDetails($rapportId)
{
    // Step 1: Retrieve the Rapport with its analyses
    $rapport = Rapport::with('analyses')->find($rapportId);

    if (!$rapport) {
        return response()->json(['message' => 'Rapport not found'], 404);
    }

    // Step 2: Extract demande_id and unique code_echantillon values
    $demandeId = $rapport->demande_id;
    $codeEchantillons = $rapport->analyses->pluck('code_echantillon')->unique()->toArray();

    // Step 3: Fetch samples matching demande_id and code_echantillon
    $samples = Sample::where('demande_id', $demandeId)
        ->whereIn('identification_echantillon', $codeEchantillons)
        ->get();

    // Step 4: Create a map of samples for quick lookup
    $sampleMap = $samples->keyBy('identification_echantillon');

    // Step 5: Return the enhanced response
    return response()->json([
        'rapport_id' => $rapport->id,
        'demande_id' => $rapport->demande_id,
        'creation_date' => $rapport->creation_date,
        'status_director' => $rapport->status_director,
        'validation' => $rapport->validation,
        'demande_numero' => $rapport->demande_numero,
        'analyses' => $rapport->analyses->map(function ($analyse) use ($sampleMap) {
            $sample = $sampleMap->get($analyse->code_echantillon);
            return [
                'id' => $analyse->id,
                'code_echantillon' => $analyse->code_echantillon,
                'parametre' => $analyse->parametre,
                'mesurande' => $analyse->mesurande,
                'unite' => $analyse->unite,
                'limite_acceptabilite' => $analyse->limite_acceptabilite,
                'methode_analyse_utilisee' => $analyse->methode_analyse_utilisee,
                'date_analyse' => $analyse->date_analyse,
                'sample' => $sample ? [
                    'id' => $sample->id,
                    'nature_echantillon' => $sample->nature_echantillon,
                    'provenance' => $sample->provenance,
                   'masse_echantillon' => $sample->masse_echantillon,
                    'etat' => $sample->etat,
                    'lot' => $sample->lot,
                    'nom_preleveur' => $sample->nom_preleveur,
                    'reference' => $sample->reference,
                    'date_prelevement' => $sample->date_prelevement,
                    'origine_prelevement' => $sample->origine_prelevement,
                    'site' => $sample->site,
                ] : null,
            ];
        }),
    ]);
}

public function createRapport(Request $request, $demandeId)
{
    // Step 1: Validate the Demande
    $demande = Demande::with('samples')->where('demande_id', $demandeId)->first();
    if (!$demande) {
        return response()->json(['message' => 'Demande not found'], 404);
    }

    // Step 2: Create Rapport
    $rapport = Rapport::create([
        'demande_id' => $demande->id,
        'creation_date' => now(),
        'status' => 'not_sent',
        'status_director' => 'not_sent',
        'demande_numero' => $demande->demande_id,
    ]);
    $demande->rapport_created = $rapport->id;
    $demande->save();

    // Step 3: Fetch FicheTransmission and its Fiches
    $ficheTransmission = FicheTransmission::where('demande_id', $demande->demande_id)->first();
    if (!$ficheTransmission) {
        return response()->json(['message' => 'FicheTransmission not found'], 404);
    }
    $fiches = $ficheTransmission->fiches()->with('sample')->get();

    // Step 4: Create Rapport Analyses for each Fiche and its Analyses
    foreach ($fiches as $fiche) {
        foreach ($fiche->analyses_demandees as $analysisName) {
            $method = $this->getAnalysisMethod($analysisName);
            RapportAnalyse::create([
                'rapport_id' => $rapport->id,
                'code_echantillon' => $fiche->sample->identification_echantillon,
                'parametre' => $analysisName,
                'mesurande' => null,
                'unite' => null,
                'limite_acceptabilite' => null,
                'methode_analyse_utilisee' => $method,
                'date_analyse' => null,
            ]);
        }
    }

    // Step 5: Update FicheTransmission status
    $ficheTransmission->update(['statusRapport' => 'created']);
}

    /**
     * Function to get the analysis method for a given analysis
     */
    private function getAnalysisMethod($analysisName)
    {
        $methods = [
            'Biotoxine (DSP)' => 'NF EN 16024: 2013 Par LC-MS/MS',
            'Biotoxine (PSP)' => 'Méthode interne validée Par LC-MS/MS',
            'Biotoxine (ASP)' => 'Accréditée selon la norme ISO/CEI 17025:2017',
            'Lipide' => 'Méthode interne validée extraction gravimétrique',
            'Acide gras' => 'ISO 12966-2:2012 / ISO 12966-4:2015 (CPG)',
            'Humidité' => 'NFV04-401',
            'Cendres' => 'NFV04-404',
            'Protéine' => 'Méthode interne validée Spectrophotométrie',
        ];


        return $methods[$analysisName] ?? 'Méthode standard';
    }
    public function updateRapportAnalyse(Request $request, $rapportId, $codeEchantillon, $analyse)
    {
        // ✅ Step 1: Validate input data
        $request->validate([
            'mesurande' => 'nullable|string',
            'unite' => 'nullable|string',
            'limite_acceptabilite' => 'nullable|string',
            'date_analyse' => 'nullable|date',
        ]);

        // ✅ Step 2: Find the specific RapportAnalyse entry
        $rapportAnalyse = RapportAnalyse::where('rapport_id', $rapportId)
            ->where('code_echantillon', $codeEchantillon)
            ->where('parametre', $analyse)
            ->first();

        if (!$rapportAnalyse) {
            return response()->json(['message' => 'Rapport Analyse not found'], 404);
        }

        // ✅ Step 3: Update fields
        $rapportAnalyse->update([
            'mesurande' => $request->input('mesurande'),
            'unite' => $request->input('unite'),
            'limite_acceptabilite' => $request->input('limite_acceptabilite'),
            'date_analyse' => $request->input('date_analyse'),
        ]);

        return response()->json([
            'message' => 'Rapport Analyse updated successfully',
            'updated_data' => $rapportAnalyse
        ]);
    }
    public function sendRapport($id)
    {
        // ✅ Find the Rapport
        $rapport = Rapport::find($id);

        if (!$rapport) {
            return response()->json(['message' => 'Rapport not found'], 404);
        }
        Notification::create([

            'title' => 'Nouvelle Rapport',
            'message' =>'Rapport pour la demande '.$rapport->demande_numero.' ',
            'demande_id'=>$rapport->demande_id,
            'type' => 'rapport receptionist',
            'rapport_id'=>$rapport->id
        ]);
        // ✅ Update the status in the database
        $rapport->update(['status' => 'sent']);

        return response()->json(['message' => 'Rapport envoyé avec succès', 'status' => 'sented']);
    }
    public function sendRapportToDirector($id)
    {

            // ✅ Find the Rapport
            $rapport = Rapport::find($id);

            if (!$rapport) {
                return response()->json(['message' => 'Rapport not found'], 404);
            }

            // Find the associated Demande
            $demande = Demande::find($rapport->demande_id);
            if (!$demande) {
                return response()->json(['message' => 'Associated demande not found'], 404);
            }

            // Create notification for the director
            $notification = Notification::create([
                'title' => 'Nouvelle Rapport',
                'message' =>'Rapport pour la demande '.$rapport->demande_numero.' ',
                'demande_id'=>$rapport->demande_id,
                'type' => 'rapport',
                'rapport_id'=>$rapport->id,
                'user_id' => $demande->user_id
            ]);

            // Get user information for the email
            $user = User::find($demande->user_id);

            // Find director to send email notification - using the same approach as in other controllers
            $director = User::where('role', 'director')->first();


                Mail::to($director->email)->send(new RapportDirectorEmail($notification, $rapport, $demande, $user));
            // ✅ Update the status in the database
            $rapport->update(['status_director' => 'sent']);

            return response()->json([
                'message' => 'Rapport envoyé avec succès',
                'status_director' => 'sent',
                'director_email' => $director ? $director->email : 'none',
                'test_email' => '<EMAIL>'
            ]);




    }

    public function getSentRapports()
    {
      // ✅ Retrieve all rapports with demande details
    $rapports = Rapport::select('id', 'demande_id', 'creation_date', 'status', 'status_director', 'validation')
    ->with('demande:id,demande_id,facture_id') // Ensure facture_id is included
    ->where('status', 'sent')
    ->get();

// ✅ Format response
$formattedRapports = $rapports->map(function ($rapport) {
    return [
        'id' => $rapport->id,
        'demande_id' => $rapport->demande ? $rapport->demande->demande_id : 'N/A',
        'creation_date' => $rapport->creation_date,
        'status' => $rapport->status,
        'status_director' => $rapport->status_director,
        'validation' => $rapport->validation,
        'facture_id' => $rapport->demande ? $rapport->demande->facture_id : 'N/A' // Ensure facture_id is retrieved correctly
    ];
});

return response()->json($formattedRapports);
    }

}
